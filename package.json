{"name": "nimbus-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tanstack/react-query": "^5.84.2", "@tanstack/react-query-devtools": "^5.84.2", "@tanstack/react-router": "^1.130.17", "@tanstack/react-router-devtools": "^1.130.17", "react": "^19.1.1", "react-dom": "^19.1.1"}, "devDependencies": {"@eslint/compat": "^1.3.2", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.32.0", "@tanstack/eslint-plugin-query": "^5.83.1", "@tanstack/router-plugin": "^1.130.17", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^4.7.0", "eslint": "^9.32.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^10.1.8", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "prettier": "^3.6.2", "typescript": "~5.8.3", "typescript-eslint": "^8.39.0", "vite": "^7.1.0"}}