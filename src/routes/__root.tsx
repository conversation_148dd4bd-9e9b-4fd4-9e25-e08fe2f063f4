import { createRootRoute, <PERSON>, Outlet } from '@tanstack/react-router';
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools';

// This is the root route of your application. It is the parent of all other routes.
// It is a good place to put your layout components that you want to share across all
// pages, like a navigation bar or a footer.
export const Route = createRootRoute({
  component: () => (
    <>
      <div>
        <Link to='/'>dashboard</Link> <Link to='/licenses'>licenses</Link>{' '}
        <Link to='/settings'>settings</Link>
      </div>
      <hr />
      <Outlet />
      <TanStackRouterDevtools />
    </>
  ),
});
